import { useAuth } from '@/contexts/AuthContext';
import { Redirect } from 'expo-router';
import { ActivityIndicator, View } from 'react-native';

export default function Index() {
  const { user, loading } = useAuth();

  console.log('Index - User:', user?.email, 'Loading:', loading);

  if (loading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <ActivityIndicator size="large" />
      </View>
    );
  }

  // Redirect based on authentication status
  if (user) {
    console.log('Redirecting to tabs');
    return <Redirect href="/(tabs)" />;
  } else {
    console.log('Redirecting to auth');
    return <Redirect href="/auth" />;
  }
}
